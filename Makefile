
.PHONY: clean build test test-iteration1 test-iteration2 test-unittest test_iteration1 test_iteration2 test_unittest release tag all bump-patch bump-minor bump-major commit-and-push publish-patch publish-minor publish-major docker-build docker-run docker-stop docker-test docker-publish docker-clean

# File paths
version_file = VERSION

# Ensure VERSION file exists with default value
$(shell if [ ! -f $(version_file) ]; then echo "1.0.0" > $(version_file); fi)

current_version = $(shell cat $(version_file) | tr -d ' ')
dev_ver = $(current_version)-SNAPSHOT

define bump_version
$(eval MAJOR=$(shell echo $(current_version) | cut -d. -f1))
$(eval MINOR=$(shell echo $(current_version) | cut -d. -f2))
$(eval PATCH=$(shell echo $(current_version) | cut -d. -f3))
endef

# Clean
clean:
	@echo "Cleaning project..."
	@mvn clean

# Build
build:
	@echo "Building project..."
	@mvn clean install -DskipTests

# Test
test:
	@echo "Running all tests (iteration1, iteration2, unittest)..."
	@$(MAKE) test-iteration1
	@$(MAKE) test-iteration2
	@$(MAKE) test-unittest

test-iteration1:
	@echo "Starting reference server with 1x1 world..."
	@java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 1 > /dev/null 2>&1 & \
	echo $$! > ref_server.pid
	@sleep 5
	@echo "Running acceptance tests..."
	@mvn -Dtest=acceptance_test.LaunchAcceptanceTest test
	@mvn -Dtest=acceptance_test.LookAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldRobotDeathPitAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldInitializationAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldPositionValidationAcceptanceTest test
	@mvn -Dtest=acceptance_test.WorldTests.WorldRobotAdditionAcceptanceTest test
	@echo "All tests completed. Now killing reference server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid
	@sleep 3

test_iteration1: test-iteration1

test-iteration2:
	@echo "Starting reference server with 2x2 world..."
	@java -jar ./libs/reference-server-0.2.3.jar -p 5000 -s 2 -o 1,1 > /dev/null 2>&1 & \
	echo $$! > ref_server.pid
	@sleep 5
	@echo "Running acceptance tests..."
	@mvn -Dtest=acceptance_test.LaunchRobotAcceptanceTest test
	@mvn -Dtest=acceptance_test.Moveforward.MoveForwardAcceptanceTest test
	@mvn -Dtest=acceptance_test.Moveforward.MoveForwardEdgeAcceptanceTest test
	@echo "Killing reference server..."
	@kill -9 `cat ref_server.pid` || true
	@rm -f ref_server.pid
	@sleep 3

test_iteration2: test-iteration2

test-unittest:
	@echo "Starting My codebase server..."
	mvn exec:java -Dexec.mainClass=za.co.wethinkcode.robots.server.MultiServers -Dexec.args="-p 5000 -s 2" > server.log 2>&1 &
	@echo $$! > our_server.pid
	@sleep 5
	@echo "Running unit tests (excluding acceptance tests)..."
	@echo "Starting tests with 60 second timeout protection..."
	@(sleep 60 && echo "Timeout reached, killing processes..." && if [ -f our_server.pid ]; then kill -9 `cat our_server.pid` 2>/dev/null || true; rm -f our_server.pid; fi) &
	@mvn test -Dtest="!acceptance_test.**"
	@echo "Killing My codebase server..."
	@echo "timeout thread interrupted, normal shutdown"
	@sleep 2
	@if [ -f our_server.pid ]; then kill -9 `cat our_server.pid` 2>/dev/null || true; rm -f our_server.pid; fi

test_unittest: test-unittest

# Release target
release:
	@echo "Switching to release version..."
	sed -i "s/<version>$(dev_ver)<\/version>/<version>$(current_version)<\/version>/" pom.xml
	mvn clean package -DskipTests
	@echo "Reverting to snapshot version..."
	sed -i "s/<version>$(current_version)<\/version>/<version>$(dev_ver)<\/version>/" pom.xml
	@cp target/robot-world-$(current_version)-jar-with-dependencies.jar libs/my-server-$(current_version).jar
	git add libs/my-server-$(current_version).jar

# Git tagging
tag:
	git tag -a release-v$(current_version) -m "Release version $(current_version)"
	git push origin release-v$(current_version)

bump-patch:
	@$(call bump_version)
	@MAJOR=$(MAJOR) MINOR=$(MINOR) PATCH=$(PATCH) current_version=$(current_version) version_file=$(version_file) bash -c '\
		NEW_VERSION=$$MAJOR.$$MINOR.$$((PATCH + 1)); \
		echo "Bumping patch: $$current_version → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $$version_file; \
		sed -i "s/<version>$$current_version-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" pom.xml; \
	'

# Bump minor version
bump-minor:
	@$(call bump_version)
	@MAJOR=$(MAJOR) MINOR=$(MINOR) current_version=$(current_version) version_file=$(version_file) bash -c '\
		NEW_VERSION=$$MAJOR.$$((MINOR + 1)).0; \
		echo "Bumping minor: $$current_version → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $$version_file; \
		sed -i "s/<version>$$current_version-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" pom.xml; \
	'

# Bump major version
bump-major:
	@$(call bump_version)
	@MAJOR=$(MAJOR) current_version=$(current_version) version_file=$(version_file) bash -c '\
		NEW_VERSION=$$((MAJOR + 1)).0.0; \
		echo "Bumping major: $$current_version → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $$version_file; \
		sed -i "s/<version>$$current_version-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" pom.xml; \
	'

commit-and-push:
	@git add .
	@git commit -m "Bump version to $(current_version)"
	@git push origin main

# All target
all: clean test build release

publish-patch: bump-patch all commit-and-push tag

publish-minor: bump-minor all commit-and-push tag

publish-major: bump-major all commit-and-push tag

# Docker targets
docker-build:
	@echo "Building Docker image..."
	@docker build -t robot-worlds-server:$(current_version) .
	@docker tag robot-worlds-server:$(current_version) robot-worlds-server:latest
	@echo "Docker image built: robot-worlds-server:$(current_version)"
	@echo "Exporting Docker image for GitLab CI artifacts..."
	@docker save robot-worlds-server:$(current_version) -o robot-worlds-server-$(current_version).tar
	@echo "Docker image exported: robot-worlds-server-$(current_version).tar"

docker-run:
	@echo "Running Docker container on port 5050..."
	@docker run -d --name robot-worlds-server -p 5050:5050 robot-worlds-server:latest
	@echo "Container started. Server available at http://localhost:5050"
	@echo "To stop: make docker-stop"

docker-stop:
	@echo "Stopping Docker container..."
	@docker stop robot-worlds-server || true
	@docker rm robot-worlds-server || true

docker-test:
	@echo "Testing Docker container..."
	@echo "Loading Docker image from tar file..."
	@if [ -f robot-worlds-server-$(current_version).tar ]; then \
		docker load -i robot-worlds-server-$(current_version).tar; \
	else \
		echo "Docker image tar not found, building image..."; \
		$(MAKE) docker-build; \
	fi
	@docker run -d --name robot-worlds-test -p 5051:5050 robot-worlds-server:$(current_version)
	@sleep 5
	@echo "Running basic connectivity test against Docker container..."
	@echo "Testing if container is responding on port 5051..."
	@timeout 10 sh -c 'until nc -z localhost 5051; do sleep 1; done' || echo "Container may not be ready"
	@echo "Docker container test completed"
	@echo "Stopping test container..."
	@docker stop robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-test 2>/dev/null || true

docker-publish:
	@echo "Publishing Docker image to GitLab Container Registry..."
	@echo "Loading Docker image from tar file..."
	@if [ -f robot-worlds-server-$(current_version).tar ]; then \
		docker load -i robot-worlds-server-$(current_version).tar; \
	else \
		echo "Docker image tar not found, building image..."; \
		$(MAKE) docker-build; \
	fi
	@docker tag robot-worlds-server:$(current_version) registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:$(current_version)
	@docker tag robot-worlds-server:$(current_version) registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
	@docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:$(current_version)
	@docker push registry.gitlab.wethinkco.de/jaarnoljhb024/brownfields_robot_worlds_3:latest
	@echo "Docker image published to GitLab Container Registry"

docker-clean:
	@echo "Cleaning up Docker images and containers..."
	@docker stop robot-worlds-server robot-worlds-test 2>/dev/null || true
	@docker rm robot-worlds-server robot-worlds-test 2>/dev/null || true
	@docker rmi robot-worlds-server:$(current_version) robot-worlds-server:latest 2>/dev/null || true
	@echo "Docker cleanup completed"